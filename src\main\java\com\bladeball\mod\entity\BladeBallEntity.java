package com.bladeball.mod.entity;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.nbt.CompoundNBT;
import net.minecraft.network.IPacket;
import net.minecraft.network.datasync.DataParameter;
import net.minecraft.network.datasync.DataSerializers;
import net.minecraft.network.datasync.EntityDataManager;
import net.minecraft.world.World;
import net.minecraftforge.fml.network.NetworkHooks;

/**
 * The Blade Ball entity that bounces between players
 * Minimal stub for 1.16.5 compatibility - to be expanded later
 */
public class BladeBallEntity extends Entity {
    private static final DataParameter<Float> SPEED = EntityDataManager.defineId(BladeBallEntity.class, DataSerializers.FLOAT);

    private float currentSpeed = 0.1f;
    
    public BladeBallEntity(EntityType<?> entityTypeIn, World worldIn) {
        super(entityTypeIn, worldIn);
        this.setNoGravity(true);
    }

    public BladeBallEntity(World world) {
        this(null, world); // Will need to register entity type properly
    }

    @Override
    protected void defineSynchedData() {
        this.entityData.define(SPEED, 0.1f);
    }

    @Override
    public void tick() {
        super.tick();

        if (level.isClientSide) {
            return;
        }

        // Basic movement - just float in place for now
        this.setDeltaMovement(0, 0, 0);
    }
    
    // Getters and setters
    public float getCurrentSpeed() {
        return currentSpeed;
    }
    
    private void findNewTarget() {
        if (arena == null) return;
        
        // Find nearest alive player
        List<ServerPlayerEntity> players = level.getEntitiesOfClass(ServerPlayerEntity.class,
            new AxisAlignedBB(position().add(-50, -50, -50), position().add(50, 50, 50)));

        ServerPlayerEntity nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (ServerPlayerEntity player : players) {
            // Check if player is in arena and alive (simplified check)
            double distance = this.distanceToSqr(player);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }

        if (nearestPlayer != null) {
            targetPlayer = nearestPlayer.getUUID();
        }
    }
    
    private void checkCollisions() {
        List<ServerPlayerEntity> players = world.getEntitiesWithinAABB(ServerPlayerEntity.class, 
            this.getBoundingBox().grow(0.5));
        
        for (ServerPlayerEntity player : players) {
            if (this.getDistanceSq(player) < 1.0) { // Within 1 block
                onPlayerHit(player);
                break;
            }
        }
    }
    
    private void onPlayerHit(ServerPlayerEntity player) {
        if (arena == null) return;
        
        // Check if player is trying to deflect (right-clicking or using ability)
        // For now, we'll assume any collision is a deflection attempt
        
        // Calculate deflection direction based on player's look direction
        Vector3d playerLook = player.getLookVec();
        Vector3d deflectionDirection = playerLook.normalize();
        
        // Add some randomness for more interesting gameplay
        double randomX = (Math.random() - 0.5) * 0.3;
        double randomZ = (Math.random() - 0.5) * 0.3;
        deflectionDirection = deflectionDirection.add(randomX, 0, randomZ).normalize();
        
        // Increase speed slightly with each deflection
        currentSpeed = Math.min(maxSpeed, currentSpeed + 0.1f);
        deflectionCount++;
        
        // Find new target in the deflection direction
        findTargetInDirection(deflectionDirection);
        
        // Update player stats
        if (arena.getGameManager() != null) {
            var playerData = arena.getGameManager().getPlayerData(player.getUniqueID());
            if (playerData != null) {
                playerData.addDeflection();
            }
        }
        
        // Spawn deflection particles
        spawnDeflectionParticles();
        
        lastDeflectionDirection = deflectionDirection;
    }
    
    private void findTargetInDirection(Vector3d direction) {
        List<ServerPlayerEntity> players = world.getEntitiesWithinAABB(ServerPlayerEntity.class, 
            new AxisAlignedBB(getPositionVec().add(-50, -50, -50), getPositionVec().add(50, 50, 50)));
        
        ServerPlayerEntity bestTarget = null;
        double bestScore = -1;
        
        for (ServerPlayerEntity player : players) {
            if (arena.getPlayerArena(player) != arena) continue;
            
            Vector3d toPlayer = player.getPositionVec().subtract(this.getPositionVec()).normalize();
            double dot = direction.dotProduct(toPlayer);
            
            // Prefer players in the deflection direction
            if (dot > bestScore) {
                bestScore = dot;
                bestTarget = player;
            }
        }
        
        if (bestTarget != null) {
            targetPlayer = bestTarget.getUniqueID();
        }
    }
    
    private boolean isWithinArenaBounds() {
        if (arena == null) return true;
        
        Vector3d center = arena.getCenter();
        double radius = arena.getRadius();
        
        double distanceFromCenter = this.getPositionVec().distanceTo(center);
        return distanceFromCenter <= radius;
    }
    
    private void bounceOffWalls() {
        if (arena == null) return;
        
        Vector3d center = arena.getCenter();
        Vector3d currentPos = this.getPositionVec();
        Vector3d fromCenter = currentPos.subtract(center).normalize();
        
        // Bounce back towards center
        Vector3d bounceDirection = fromCenter.scale(-1);
        this.setMotion(bounceDirection.scale(currentSpeed));
        
        // Find new target
        findTargetInDirection(bounceDirection);
    }
    
    private void spawnParticles() {
        if (world.isRemote) {
            // Spawn trail particles
            world.addParticle(ParticleTypes.FLAME,
                    getPosX(), getPosY(), getPosZ(),
                    0, 0, 0);
            
            if (this.dataManager.get(IS_TARGETING)) {
                // Spawn targeting particles
                world.addParticle(ParticleTypes.CRIT,
                        getPosX(), getPosY(), getPosZ(),
                        0, 0, 0);
            }
        }
    }
    
    private void spawnDeflectionParticles() {
        if (!world.isRemote) {
            ((ServerWorld) world).spawnParticle(ParticleTypes.EXPLOSION,
                    getPosX(), getPosY(), getPosZ(),
                    5, 0.5, 0.5, 0.5, 0.1);
        }
    }
    
    // Setters and getters
    public void setArena(BladeBallArena arena) {
        this.arena = arena;
    }
    
    public void setTargetPlayer(UUID targetPlayer) {
        this.targetPlayer = targetPlayer;
    }
    
    public UUID getTargetPlayer() {
        return targetPlayer;
    }
    
    public float getCurrentSpeed() {
        return currentSpeed;
    }
    
    public int getDeflectionCount() {
        return deflectionCount;
    }
    
    @Override
    protected void readAdditional(CompoundNBT compound) {
        currentSpeed = compound.getFloat("Speed");
        deflectionCount = compound.getInt("DeflectionCount");
        ticksAlive = compound.getInt("TicksAlive");
        
        if (compound.hasUniqueId("TargetPlayer")) {
            targetPlayer = compound.getUniqueId("TargetPlayer");
        }
    }
    
    @Override
    protected void writeAdditional(CompoundNBT compound) {
        compound.putFloat("Speed", currentSpeed);
        compound.putInt("DeflectionCount", deflectionCount);
        compound.putInt("TicksAlive", ticksAlive);
        
        if (targetPlayer != null) {
            compound.putUniqueId("TargetPlayer", targetPlayer);
        }
    }
    
    @Override
    public IPacket<?> createSpawnPacket() {
        return NetworkHooks.getEntitySpawningPacket(this);
    }
}
