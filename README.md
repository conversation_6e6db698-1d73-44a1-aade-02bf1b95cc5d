# Blade Ball Minecraft Mod

A competitive survival gamemode for Minecraft inspired by the popular Roblox game Blade Ball. Players must deflect a bouncing ball to survive and be the last player standing!

## Features

### Core Gameplay
- **Ball Physics**: A ball that bounces between players, accelerating with each deflection
- **Deflection System**: Right-click to deflect the ball away from yourself
- **Elimination**: Get hit by the ball and you're out!
- **Last Player Standing**: Be the sole survivor to win the round

### Abilities System
Players can use special abilities with cooldowns:
- **Dash**: Quickly move forward in the direction you're looking (3s cooldown)
- **Invisibility**: Become invisible for 5 seconds to avoid the ball (15s cooldown)
- **Forcefield**: Create a protective barrier that automatically deflects the ball (20s cooldown)
- **Freeze**: Freeze all nearby players for 3 seconds (25s cooldown)

### Arena System
- **Multi-Arena Support**: Multiple games can run simultaneously
- **Configurable Arenas**: Customizable size, spawn points, and boundaries
- **Spectator Mode**: Watch the action after elimination
- **Auto-Scaling**: Arenas adjust to player count

### Game States
- **Waiting**: Players join and prepare
- **Starting**: Countdown before game begins
- **Active**: Game in progress
- **Ending**: Results and cleanup

## Commands

### Player Commands
- `/bladeball join [arena]` - Join a specific arena
- `/bladeball join` - Join any available arena (creates new if needed)
- `/bladeball leave` - Leave current arena
- `/bladeball stats` - View your statistics
- `/bladeball ability` - Use your selected ability
- `/bladeball help` - Show command help

### Admin Commands
- `/bladeball create [arena]` - Create a new arena
- `/bladeball list` - List all arenas and their status

## Controls

- **Right-Click**: Deflect the ball (when near you)
- **Shift + Right-Click**: Use your selected ability
- **Movement**: Standard WASD movement

## Game Rules

1. **Objective**: Be the last player standing
2. **Deflection**: Right-click when the ball is near to deflect it
3. **Targeting**: The ball targets the nearest player to where it was deflected
4. **Speed**: The ball accelerates with each deflection
5. **Abilities**: Use abilities strategically with cooldowns
6. **Arena Bounds**: Stay within the arena boundaries
7. **Elimination**: Getting hit by the ball eliminates you from the round

## Statistics Tracking

The mod tracks various player statistics:
- **Wins**: Number of games won
- **Games Played**: Total games participated in
- **Win Rate**: Percentage of games won
- **Ball Deflections**: Total successful deflections
- **Eliminations**: Number of players eliminated

## Technical Details

### Requirements
- Minecraft 1.16.5
- Minecraft Forge 36.2.42+
- Java 8+

### Installation
1. Download the mod JAR file
2. Place it in your `mods` folder
3. Start Minecraft with Forge

### Configuration
Arenas can be configured with:
- Center position
- Radius and height
- Player limits (2-8 players)
- Game duration limits

## Development

### Building
```bash
./gradlew build
```

### Project Structure
- `game/` - Core game logic and arena management
- `abilities/` - Player ability system
- `entity/` - Ball entity and physics
- `commands/` - Command system
- `events/` - Event handlers

### Key Classes
- `BladeBallGameManager` - Main game coordinator
- `BladeBallArena` - Individual arena instance
- `BladeBallEntity` - The ball entity
- `BladeBallPlayerData` - Player statistics and state
- `AbilityBase` - Base class for all abilities

## Future Features

Planned additions:
- More abilities (teleport, shield, speed boost, etc.)
- Arena editor GUI
- Tournaments and ranked play
- Custom ball skins and effects
- Team-based modes
- Power-ups and special events
- Replay system
- Advanced statistics and leaderboards

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

Inspired by the original Blade Ball game on Roblox. This is an unofficial recreation for Minecraft.
