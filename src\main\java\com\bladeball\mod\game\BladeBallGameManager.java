package com.bladeball.mod.game;

import com.bladeball.mod.game.arena.BladeBallArena;
import com.bladeball.mod.game.player.BladeBallPlayerData;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.server.ServerWorld;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Main game manager for Blade Ball
 * Handles multiple arenas, player management, and game states
 */
public class BladeBallGameManager {
    private static final Logger LOGGER = LogManager.getLogger();
    
    // Map of arena ID to arena instance
    private final Map<String, BladeBallArena> arenas = new ConcurrentHashMap<>();
    
    // Map of player UUID to their current arena
    private final Map<UUID, String> playerArenaMap = new ConcurrentHashMap<>();
    
    // Map of player UUID to their game data
    private final Map<UUID, BladeBallPlayerData> playerDataMap = new ConcurrentHashMap<>();
    
    // Default arena counter for auto-generated arenas
    private int arenaCounter = 0;
    
    public BladeBallGameManager() {
        LOGGER.info("Blade Ball Game Manager initialized");
    }
    
    /**
     * Creates a new arena with the given ID
     */
    public BladeBallArena createArena(String arenaId, ServerWorld world) {
        if (arenas.containsKey(arenaId)) {
            LOGGER.warn("Arena {} already exists!", arenaId);
            return arenas.get(arenaId);
        }
        
        BladeBallArena arena = new BladeBallArena(arenaId, world, this);
        arenas.put(arenaId, arena);
        LOGGER.info("Created arena: {}", arenaId);
        return arena;
    }
    
    /**
     * Creates a new arena with auto-generated ID
     */
    public BladeBallArena createArena(ServerWorld world) {
        String arenaId = "arena_" + (++arenaCounter);
        return createArena(arenaId, world);
    }
    
    /**
     * Gets an arena by ID
     */
    public BladeBallArena getArena(String arenaId) {
        return arenas.get(arenaId);
    }
    
    /**
     * Gets all arenas
     */
    public Collection<BladeBallArena> getAllArenas() {
        return arenas.values();
    }
    
    /**
     * Removes an arena
     */
    public void removeArena(String arenaId) {
        BladeBallArena arena = arenas.remove(arenaId);
        if (arena != null) {
            arena.shutdown();
            LOGGER.info("Removed arena: {}", arenaId);
        }
    }
    
    /**
     * Adds a player to an arena
     */
    public boolean addPlayerToArena(ServerPlayerEntity player, String arenaId) {
        BladeBallArena arena = arenas.get(arenaId);
        if (arena == null) {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "Arena not found!"), player.getUUID());
            return false;
        }

        // Remove player from current arena if they're in one
        removePlayerFromCurrentArena(player);

        // Add to new arena
        if (arena.addPlayer(player)) {
            playerArenaMap.put(player.getUUID(), arenaId);

            // Create or get player data
            BladeBallPlayerData playerData = playerDataMap.computeIfAbsent(
                player.getUUID(),
                uuid -> new BladeBallPlayerData(uuid)
            );

            player.sendMessage(new StringTextComponent(TextFormatting.GREEN + "Joined arena: " + arenaId), player.getUUID());
            return true;
        }
        
        return false;
    }
    
    /**
     * Removes a player from their current arena
     */
    public void removePlayerFromCurrentArena(ServerPlayerEntity player) {
        String currentArenaId = playerArenaMap.remove(player.getUUID());
        if (currentArenaId != null) {
            BladeBallArena arena = arenas.get(currentArenaId);
            if (arena != null) {
                arena.removePlayer(player);
            }
        }
    }
    
    /**
     * Gets the arena a player is currently in
     */
    public BladeBallArena getPlayerArena(ServerPlayerEntity player) {
        String arenaId = playerArenaMap.get(player.getUUID());
        return arenaId != null ? arenas.get(arenaId) : null;
    }
    
    /**
     * Gets player data
     */
    public BladeBallPlayerData getPlayerData(UUID playerUuid) {
        return playerDataMap.get(playerUuid);
    }
    
    /**
     * Gets player data, creating if it doesn't exist
     */
    public BladeBallPlayerData getOrCreatePlayerData(UUID playerUuid) {
        return playerDataMap.computeIfAbsent(playerUuid, BladeBallPlayerData::new);
    }
    
    /**
     * Finds an available arena for a player to join
     */
    public BladeBallArena findAvailableArena() {
        return arenas.values().stream()
            .filter(arena -> arena.getGameState() == GameState.WAITING && arena.canAddPlayer())
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Updates all arenas (called from server tick)
     */
    public void tick() {
        arenas.values().forEach(BladeBallArena::tick);
    }
    
    /**
     * Handles player disconnect
     */
    public void onPlayerDisconnect(ServerPlayerEntity player) {
        removePlayerFromCurrentArena(player);
        playerDataMap.remove(player.getUUID());
    }
    
    /**
     * Gets statistics about all arenas
     */
    public String getArenaStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Blade Ball Arena Statistics ===\n");
        stats.append("Total Arenas: ").append(arenas.size()).append("\n");
        
        for (BladeBallArena arena : arenas.values()) {
            stats.append("Arena ").append(arena.getId())
                 .append(": ").append(arena.getPlayerCount()).append(" players, ")
                 .append("State: ").append(arena.getGameState())
                 .append("\n");
        }
        
        return stats.toString();
    }
    
    /**
     * Shuts down all arenas
     */
    public void shutdown() {
        LOGGER.info("Shutting down Blade Ball Game Manager");
        arenas.values().forEach(BladeBallArena::shutdown);
        arenas.clear();
        playerArenaMap.clear();
        playerDataMap.clear();
    }
}
