package com.bladeball.mod.commands;

import com.bladeball.mod.BladeBallMod;
import com.bladeball.mod.game.BladeBallGameManager;
import com.bladeball.mod.game.arena.BladeBallArena;
import com.bladeball.mod.game.player.BladeBallPlayerData;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.command.CommandSource;
import net.minecraft.command.Commands;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

/**
 * Commands for Blade Ball
 */
public class BladeBallCommands {
    
    public static void register(CommandDispatcher<CommandSource> dispatcher) {
        dispatcher.register(Commands.literal("bladeball")
            .then(Commands.literal("join")
                .then(Commands.argument("arena", StringArgumentType.string())
                    .executes(BladeBallCommands::joinArena))
                .executes(BladeBallCommands::joinAnyArena))
            .then(Commands.literal("leave")
                .executes(BladeBallCommands::leaveArena))
            .then(Commands.literal("create")
                .then(Commands.argument("arena", StringArgumentType.string())
                    .executes(BladeBallCommands::createArena))
                .executes(BladeBallCommands::createArenaAuto))
            .then(Commands.literal("list")
                .executes(BladeBallCommands::listArenas))
            .then(Commands.literal("stats")
                .executes(BladeBallCommands::showStats))
            .then(Commands.literal("ability")
                .executes(BladeBallCommands::useAbility))
            .then(Commands.literal("help")
                .executes(BladeBallCommands::showHelp)));
    }
    
    private static int joinArena(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        String arenaId = StringArgumentType.getString(context, "arena");
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        if (gameManager.addPlayerToArena(player, arenaId)) {
            return 1;
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "Failed to join arena: " + arenaId), player.getUUID());
            return 0;
        }
    }
    
    private static int joinAnyArena(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallArena arena = gameManager.findAvailableArena();
        
        if (arena != null) {
            if (gameManager.addPlayerToArena(player, arena.getId())) {
                return 1;
            }
        } else {
            // Create a new arena
            arena = gameManager.createArena(player.getLevel());
            if (gameManager.addPlayerToArena(player, arena.getId())) {
                player.sendMessage(new StringTextComponent(TextFormatting.GREEN + "Created and joined new arena: " + arena.getId()), player.getUUID());
                return 1;
            }
        }

        player.sendMessage(new StringTextComponent(TextFormatting.RED + "Failed to join any arena"), player.getUUID());
        return 0;
    }
    
    private static int leaveArena(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallArena currentArena = gameManager.getPlayerArena(player);
        
        if (currentArena != null) {
            gameManager.removePlayerFromCurrentArena(player);
            player.sendMessage(new StringTextComponent(TextFormatting.YELLOW + "Left arena: " + currentArena.getId()), player.getUUID());
            return 1;
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "You are not in any arena"), player.getUUID());
            return 0;
        }
    }
    
    private static int createArena(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        String arenaId = StringArgumentType.getString(context, "arena");
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallArena arena = gameManager.createArena(arenaId, player.getLevel());

        if (arena != null) {
            player.sendMessage(new StringTextComponent(TextFormatting.GREEN + "Created arena: " + arenaId), player.getUUID());
            return 1;
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "Failed to create arena: " + arenaId), player.getUUID());
            return 0;
        }
    }
    
    private static int createArenaAuto(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallArena arena = gameManager.createArena(player.getLevel());

        player.sendMessage(new StringTextComponent(TextFormatting.GREEN + "Created arena: " + arena.getId()), player.getUUID());
        return 1;
    }
    
    private static int listArenas(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        String stats = gameManager.getArenaStats();
        
        player.sendMessage(new StringTextComponent(TextFormatting.AQUA + stats), player.getUUID());
        return 1;
    }
    
    private static int showStats(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallPlayerData playerData = gameManager.getPlayerData(player.getUUID());

        if (playerData != null) {
            String stats = playerData.getStatsString();
            player.sendMessage(new StringTextComponent(TextFormatting.GOLD + "Your Blade Ball Stats:\n" + TextFormatting.WHITE + stats), player.getUUID());
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.YELLOW + "No stats available. Play some games first!"), player.getUUID());
        }
        
        return 1;
    }
    
    private static int useAbility(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        BladeBallPlayerData playerData = gameManager.getPlayerData(player.getUUID());

        if (playerData == null) {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "Player data not found"), player.getUUID());
            return 0;
        }

        if (!playerData.canUseAbility()) {
            long remainingMs = playerData.getRemainingCooldown();
            double remainingSeconds = remainingMs / 1000.0;
            player.sendMessage(new StringTextComponent(TextFormatting.RED + String.format("Ability on cooldown! %.1fs remaining", remainingSeconds)), player.getUUID());
            return 0;
        }

        if (playerData.getSelectedAbility() != null) {
            if (playerData.getSelectedAbility().execute(player)) {
                playerData.useAbility();
                return 1;
            }
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "No ability selected"), player.getUUID());
        }
        
        return 0;
    }
    
    private static int showHelp(CommandContext<CommandSource> context) throws CommandSyntaxException {
        ServerPlayerEntity player = context.getSource().asPlayer();
        
        StringBuilder help = new StringBuilder();
        help.append(TextFormatting.GOLD).append("=== Blade Ball Commands ===\n");
        help.append(TextFormatting.YELLOW).append("/bladeball join [arena]").append(TextFormatting.WHITE).append(" - Join an arena\n");
        help.append(TextFormatting.YELLOW).append("/bladeball leave").append(TextFormatting.WHITE).append(" - Leave current arena\n");
        help.append(TextFormatting.YELLOW).append("/bladeball create [arena]").append(TextFormatting.WHITE).append(" - Create an arena\n");
        help.append(TextFormatting.YELLOW).append("/bladeball list").append(TextFormatting.WHITE).append(" - List all arenas\n");
        help.append(TextFormatting.YELLOW).append("/bladeball stats").append(TextFormatting.WHITE).append(" - Show your stats\n");
        help.append(TextFormatting.YELLOW).append("/bladeball ability").append(TextFormatting.WHITE).append(" - Use your ability\n");
        help.append(TextFormatting.YELLOW).append("/bladeball help").append(TextFormatting.WHITE).append(" - Show this help\n");
        help.append(TextFormatting.GRAY).append("\nGame Rules:\n");
        help.append(TextFormatting.WHITE).append("- Deflect the ball to survive\n");
        help.append(TextFormatting.WHITE).append("- Last player standing wins\n");
        help.append(TextFormatting.WHITE).append("- Use abilities strategically\n");
        help.append(TextFormatting.WHITE).append("- Right-click to deflect the ball");
        
        player.sendMessage(new StringTextComponent(help.toString()), player.getUUID());
        return 1;
    }
}
