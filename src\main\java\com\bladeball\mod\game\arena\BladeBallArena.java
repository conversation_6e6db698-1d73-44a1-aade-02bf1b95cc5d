package com.bladeball.mod.game.arena;

import com.bladeball.mod.entity.BladeBallEntity;
import com.bladeball.mod.game.BladeBallGameManager;
import com.bladeball.mod.game.GameState;
import com.bladeball.mod.game.player.BladeBallPlayerData;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.vector.Vector3d;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.GameType;
import net.minecraft.world.server.ServerWorld;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents a single Blade Ball arena instance
 */
public class BladeBallArena {
    private static final Logger LOGGER = LogManager.getLogger();
    
    // Arena configuration
    private final String id;
    private final ServerWorld world;
    private final BladeBallGameManager gameManager;
    
    // Game settings
    private static final int MIN_PLAYERS = 2;
    private static final int MAX_PLAYERS = 8;
    private static final int COUNTDOWN_DURATION = 10; // seconds
    private static final int GAME_DURATION = 300; // 5 minutes max
    
    // Arena bounds (default values, should be configurable)
    private Vector3d center = new Vector3d(0, 100, 0);
    private double radius = 20.0;
    private double height = 10.0;
    
    // Game state
    private GameState gameState = GameState.WAITING;
    private int countdown = 0;
    private int gameTime = 0;
    
    // Players
    private final Set<UUID> players = ConcurrentHashMap.newKeySet();
    private final Set<UUID> alivePlayers = ConcurrentHashMap.newKeySet();
    private final Set<UUID> spectators = ConcurrentHashMap.newKeySet();
    
    // Game entities
    private BladeBallEntity ball;
    private UUID ballTarget; // UUID of player the ball is targeting
    
    // Spawn points around the arena
    private final List<Vector3d> spawnPoints = new ArrayList<>();
    
    public BladeBallArena(String id, ServerWorld world, BladeBallGameManager gameManager) {
        this.id = id;
        this.world = world;
        this.gameManager = gameManager;
        
        generateSpawnPoints();
        LOGGER.info("Created arena {} at {}", id, center);
    }
    
    /**
     * Generates spawn points around the arena perimeter
     */
    private void generateSpawnPoints() {
        spawnPoints.clear();
        int numPoints = MAX_PLAYERS;
        for (int i = 0; i < numPoints; i++) {
            double angle = (2 * Math.PI * i) / numPoints;
            double x = center.x + (radius - 3) * Math.cos(angle);
            double z = center.z + (radius - 3) * Math.sin(angle);
            double y = center.y + 1;
            spawnPoints.add(new Vector3d(x, y, z));
        }
    }
    
    /**
     * Adds a player to the arena
     */
    public boolean addPlayer(ServerPlayerEntity player) {
        if (players.size() >= MAX_PLAYERS) {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "Arena is full!"), player.getUUID());
            return false;
        }

        if (gameState != GameState.WAITING && gameState != GameState.STARTING) {
            // Add as spectator if game is running
            return addSpectator(player);
        }

        players.add(player.getUUID());
        alivePlayers.add(player.getUUID());

        // Teleport to spawn point
        Vector3d spawnPoint = getSpawnPoint(players.size() - 1);
        player.teleportTo(spawnPoint.x, spawnPoint.y, spawnPoint.z);

        // Set game mode
        player.setGameMode(GameType.ADVENTURE);
        
        // Notify all players
        broadcastMessage(TextFormatting.YELLOW + player.getDisplayName().getString() + " joined the arena! (" + players.size() + "/" + MAX_PLAYERS + ")");
        
        // Check if we can start
        if (players.size() >= MIN_PLAYERS && gameState == GameState.WAITING) {
            startCountdown();
        }
        
        return true;
    }
    
    /**
     * Adds a player as spectator
     */
    public boolean addSpectator(ServerPlayerEntity player) {
        spectators.add(player.getUUID());

        // Teleport above arena center
        Vector3d spectatorPos = center.add(0, height + 5, 0);
        player.teleportTo(spectatorPos.x, spectatorPos.y, spectatorPos.z);
        player.setGameMode(GameType.SPECTATOR);

        player.sendMessage(new StringTextComponent(TextFormatting.GRAY + "You are now spectating the arena."), player.getUUID());
        return true;
    }
    
    /**
     * Removes a player from the arena
     */
    public void removePlayer(ServerPlayerEntity player) {
        UUID playerId = player.getUUID();
        boolean wasPlaying = players.remove(playerId);
        alivePlayers.remove(playerId);
        spectators.remove(playerId);

        if (wasPlaying) {
            broadcastMessage(TextFormatting.YELLOW + player.getDisplayName().getString() + " left the arena!");

            // Check if game should end
            if (gameState == GameState.ACTIVE && alivePlayers.size() <= 1) {
                endGame();
            } else if (gameState == GameState.STARTING && players.size() < MIN_PLAYERS) {
                cancelCountdown();
            }
        }

        // Reset player state
        player.setGameMode(GameType.SURVIVAL);
    }
    
    /**
     * Eliminates a player from the game
     */
    public void eliminatePlayer(ServerPlayerEntity player) {
        UUID playerId = player.getUUID();
        if (!alivePlayers.remove(playerId)) {
            return; // Player wasn't alive
        }

        // Add to spectators
        spectators.add(playerId);
        player.setGameMode(GameType.SPECTATOR);

        // Teleport to spectator position
        Vector3d spectatorPos = center.add(0, height + 5, 0);
        player.teleportTo(spectatorPos.x, spectatorPos.y, spectatorPos.z);
        
        broadcastMessage(TextFormatting.RED + player.getDisplayName().getString() + " was eliminated!");
        
        // Check if game should end
        if (alivePlayers.size() <= 1) {
            endGame();
        }
    }
    
    /**
     * Starts the countdown
     */
    private void startCountdown() {
        gameState = GameState.STARTING;
        countdown = COUNTDOWN_DURATION;
        broadcastMessage(TextFormatting.GREEN + "Game starting in " + countdown + " seconds!");
    }
    
    /**
     * Cancels the countdown
     */
    private void cancelCountdown() {
        gameState = GameState.WAITING;
        countdown = 0;
        broadcastMessage(TextFormatting.YELLOW + "Countdown cancelled - not enough players!");
    }
    
    /**
     * Starts the game
     */
    private void startGame() {
        gameState = GameState.ACTIVE;
        gameTime = 0;
        
        broadcastMessage(TextFormatting.GREEN + "Game started! Deflect the ball to survive!");
        
        // Spawn the ball
        spawnBall();
    }
    
    /**
     * Spawns the ball in the center of the arena
     */
    private void spawnBall() {
        if (ball != null) {
            ball.remove();
        }
        
        // Create ball entity (will implement this next)
        // ball = new BladeBallEntity(world);
        // ball.setPosition(center.x, center.y + 2, center.z);
        // world.addEntity(ball);
        
        // Select random target
        if (!alivePlayers.isEmpty()) {
            ballTarget = alivePlayers.iterator().next();
        }
        
        LOGGER.info("Ball spawned in arena {}", id);
    }
    
    /**
     * Ends the game
     */
    private void endGame() {
        gameState = GameState.ENDING;
        
        // Determine winner
        if (alivePlayers.size() == 1) {
            UUID winnerId = alivePlayers.iterator().next();
            ServerPlayerEntity winner = world.getServer().getPlayerList().getPlayer(winnerId);
            if (winner != null) {
                broadcastMessage(TextFormatting.GOLD + winner.getDisplayName().getString() + " wins!");

                // Update player stats
                BladeBallPlayerData playerData = gameManager.getPlayerData(winnerId);
                if (playerData != null) {
                    playerData.addWin();
                }
            }
        } else {
            broadcastMessage(TextFormatting.GRAY + "Game ended with no winner!");
        }
        
        // Remove ball
        if (ball != null) {
            ball.remove();
            ball = null;
        }
        
        // Schedule reset
        countdown = 5; // 5 seconds to show results
    }
    
    /**
     * Resets the arena for a new game
     */
    private void resetArena() {
        gameState = GameState.WAITING;
        countdown = 0;
        gameTime = 0;
        
        // Move all players back to spawn points
        int spawnIndex = 0;
        for (UUID playerId : players) {
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerId);
            if (player != null) {
                Vector3d spawnPoint = getSpawnPoint(spawnIndex++);
                player.teleportTo(spawnPoint.x, spawnPoint.y, spawnPoint.z);
                player.setGameMode(GameType.ADVENTURE);
            }
        }
        
        // Reset alive players
        alivePlayers.clear();
        alivePlayers.addAll(players);
        
        // Clear spectators
        spectators.clear();
        
        broadcastMessage(TextFormatting.GREEN + "Arena reset! Waiting for players...");
    }
    
    /**
     * Main tick method called every server tick
     */
    public void tick() {
        switch (gameState) {
            case STARTING:
                tickCountdown();
                break;
            case ACTIVE:
                tickGame();
                break;
            case ENDING:
                tickEnding();
                break;
        }
    }
    
    private void tickCountdown() {
        if (countdown > 0) {
            countdown--;
            if (countdown == 0) {
                startGame();
            } else if (countdown <= 5) {
                broadcastMessage(TextFormatting.YELLOW + "Starting in " + countdown + "...");
            }
        }
    }
    
    private void tickGame() {
        gameTime++;
        
        // Check for game timeout
        if (gameTime >= GAME_DURATION * 20) { // Convert seconds to ticks
            broadcastMessage(TextFormatting.RED + "Game timed out!");
            endGame();
            return;
        }
        
        // Update ball logic here
        // TODO: Implement ball physics and targeting
    }
    
    private void tickEnding() {
        if (countdown > 0) {
            countdown--;
            if (countdown == 0) {
                resetArena();
            }
        }
    }
    
    /**
     * Broadcasts a message to all players in the arena
     */
    public void broadcastMessage(String message) {
        StringTextComponent textComponent = new StringTextComponent(message);
        
        for (UUID playerId : players) {
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerId);
            if (player != null) {
                player.sendMessage(textComponent, playerId);
            }
        }

        for (UUID spectatorId : spectators) {
            ServerPlayerEntity spectator = world.getServer().getPlayerList().getPlayer(spectatorId);
            if (spectator != null) {
                spectator.sendMessage(textComponent, spectatorId);
            }
        }
    }
    
    /**
     * Gets a spawn point by index
     */
    private Vector3d getSpawnPoint(int index) {
        if (spawnPoints.isEmpty()) {
            return center.add(0, 1, 0);
        }
        return spawnPoints.get(index % spawnPoints.size());
    }
    
    // Getters
    public String getId() { return id; }
    public GameState getGameState() { return gameState; }
    public int getPlayerCount() { return players.size(); }
    public boolean canAddPlayer() { return players.size() < MAX_PLAYERS; }
    public ServerWorld getWorld() { return world; }
    public Vector3d getCenter() { return center; }
    public double getRadius() { return radius; }
    
    // Setters for configuration
    public void setCenter(Vector3d center) { 
        this.center = center; 
        generateSpawnPoints();
    }
    
    public void setRadius(double radius) { 
        this.radius = radius; 
        generateSpawnPoints();
    }
    
    public void setHeight(double height) { 
        this.height = height; 
    }
    
    /**
     * Shuts down the arena
     */
    public void shutdown() {
        // Remove all players
        for (UUID playerId : new HashSet<>(players)) {
            ServerPlayerEntity player = world.getServer().getPlayerList().getPlayer(playerId);
            if (player != null) {
                removePlayer(player);
            }
        }
        
        // Remove ball
        if (ball != null) {
            ball.remove();
        }
        
        gameState = GameState.DISABLED;
        LOGGER.info("Arena {} shut down", id);
    }
}
