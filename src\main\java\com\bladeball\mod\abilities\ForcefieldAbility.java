package com.bladeball.mod.abilities;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

/**
 * Forcefield ability - creates a protective barrier that automatically deflects the ball
 */
public class ForcefieldAbility extends AbilityBase {
    private static final int DURATION_TICKS = 60; // 3 seconds
    
    public ForcefieldAbility() {
        super("Forcefield", 
              "Create a protective barrier that automatically deflects the ball", 
              20000, // 20 second cooldown
              AbilityType.DEFENSIVE);
    }
    
    @Override
    public boolean execute(ServerPlayerEntity player) {
        if (!canUse(player)) {
            return false;
        }
        
        // Apply resistance effect to represent the forcefield
        EffectInstance resistance = new EffectInstance(Effects.RESISTANCE, DURATION_TICKS, 4, false, false, true);
        player.addPotionEffect(resistance);
        
        // Apply glowing effect for visual feedback
        EffectInstance glowing = new EffectInstance(Effects.GLOWING, DURATION_TICKS, 0, false, false, true);
        player.addPotionEffect(glowing);
        
        // Spawn forcefield particles
        spawnForcefieldParticles(player);
        
        sendSuccessMessage(player, "Forcefield activated!");
        return true;
    }
    
    private void spawnForcefieldParticles(ServerPlayerEntity player) {
        ServerWorld world = player.getServerWorld();
        
        // Create a sphere of particles around the player
        int particleCount = 30;
        for (int i = 0; i < particleCount; i++) {
            double angle1 = Math.random() * 2 * Math.PI;
            double angle2 = Math.random() * Math.PI;
            double radius = 2.0;
            
            double x = radius * Math.sin(angle2) * Math.cos(angle1);
            double y = radius * Math.cos(angle2);
            double z = radius * Math.sin(angle2) * Math.sin(angle1);
            
            world.spawnParticle(ParticleTypes.ENCHANT,
                    player.getPosX() + x, 
                    player.getPosY() + 1 + y, 
                    player.getPosZ() + z,
                    1, 0, 0, 0, 0);
        }
    }
    
    @Override
    public boolean canUse(ServerPlayerEntity player) {
        // Can't use if already has resistance (forcefield active)
        if (player.isPotionActive(Effects.RESISTANCE)) {
            sendErrorMessage(player, "Forcefield is already active!");
            return false;
        }
        
        return super.canUse(player);
    }
}
