package com.bladeball.mod.game.player;

import com.bladeball.mod.abilities.AbilityBase;
import com.bladeball.mod.abilities.DashAbility;

import java.util.UUID;

/**
 * Stores data for a player in Blade Ball
 */
public class BladeBallPlayerData {
    private final UUID playerUuid;
    
    // Statistics
    private int wins = 0;
    private int gamesPlayed = 0;
    private int eliminations = 0;
    private int ballDeflections = 0;
    
    // Current game state
    private boolean isAlive = true;
    private boolean isSpectating = false;
    
    // Abilities
    private AbilityBase selectedAbility;
    private long lastAbilityUse = 0;
    
    // Deflection stats for current game
    private int currentGameDeflections = 0;
    private long lastDeflectionTime = 0;
    
    public BladeBallPlayerData(UUID playerUuid) {
        this.playerUuid = playerUuid;
        // Default ability is dash
        this.selectedAbility = new DashAbility();
    }
    
    // Statistics methods
    public void addWin() {
        wins++;
        gamesPlayed++;
    }
    
    public void addLoss() {
        gamesPlayed++;
    }
    
    public void addElimination() {
        eliminations++;
    }
    
    public void addDeflection() {
        ballDeflections++;
        currentGameDeflections++;
        lastDeflectionTime = System.currentTimeMillis();
    }
    
    // Game state methods
    public void resetForNewGame() {
        isAlive = true;
        isSpectating = false;
        currentGameDeflections = 0;
        lastDeflectionTime = 0;
        lastAbilityUse = 0;
    }
    
    public void eliminate() {
        isAlive = false;
        isSpectating = true;
    }
    
    // Ability methods
    public boolean canUseAbility() {
        if (selectedAbility == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        long cooldownMs = selectedAbility.getCooldownMs();
        
        return (currentTime - lastAbilityUse) >= cooldownMs;
    }
    
    public boolean useAbility() {
        if (!canUseAbility()) {
            return false;
        }
        
        lastAbilityUse = System.currentTimeMillis();
        return true;
    }
    
    public long getRemainingCooldown() {
        if (selectedAbility == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long cooldownMs = selectedAbility.getCooldownMs();
        long timeSinceUse = currentTime - lastAbilityUse;
        
        return Math.max(0, cooldownMs - timeSinceUse);
    }
    
    // Getters and setters
    public UUID getPlayerUuid() {
        return playerUuid;
    }
    
    public int getWins() {
        return wins;
    }
    
    public int getGamesPlayed() {
        return gamesPlayed;
    }
    
    public int getEliminations() {
        return eliminations;
    }
    
    public int getBallDeflections() {
        return ballDeflections;
    }
    
    public boolean isAlive() {
        return isAlive;
    }
    
    public void setAlive(boolean alive) {
        isAlive = alive;
    }
    
    public boolean isSpectating() {
        return isSpectating;
    }
    
    public void setSpectating(boolean spectating) {
        isSpectating = spectating;
    }
    
    public AbilityBase getSelectedAbility() {
        return selectedAbility;
    }
    
    public void setSelectedAbility(AbilityBase selectedAbility) {
        this.selectedAbility = selectedAbility;
    }
    
    public int getCurrentGameDeflections() {
        return currentGameDeflections;
    }
    
    public long getLastDeflectionTime() {
        return lastDeflectionTime;
    }
    
    public double getWinRate() {
        if (gamesPlayed == 0) {
            return 0.0;
        }
        return (double) wins / gamesPlayed;
    }
    
    public String getStatsString() {
        return String.format("Wins: %d | Games: %d | Win Rate: %.1f%% | Deflections: %d | Eliminations: %d",
                wins, gamesPlayed, getWinRate() * 100, ballDeflections, eliminations);
    }
}
