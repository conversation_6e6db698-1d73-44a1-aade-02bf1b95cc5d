package com.bladeball.mod.game;

/**
 * Represents the different states a Blade Ball game can be in
 */
public enum GameState {
    /**
     * Waiting for players to join
     */
    WAITING,
    
    /**
     * Countdown before game starts
     */
    STARTING,
    
    /**
     * Game is actively running
     */
    ACTIVE,
    
    /**
     * Game has ended, showing results
     */
    ENDING,
    
    /**
     * Arena is disabled/shutdown
     */
    DISABLED
}
