package com.bladeball.mod.events;

import com.bladeball.mod.BladeBallMod;
import com.bladeball.mod.game.BladeBallGameManager;
import com.bladeball.mod.game.arena.BladeBallArena;
import com.bladeball.mod.game.player.BladeBallPlayerData;
import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.Hand;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * Event handler for Blade Ball game events
 */
@Mod.EventBusSubscriber(modid = BladeBallMod.MOD_ID)
public class BladeBallEventHandler {
    
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            BladeBallGameManager gameManager = BladeBallMod.getGameManager();
            if (gameManager != null) {
                gameManager.tick();
            }
        }
    }
    
    @SubscribeEvent
    public static void onPlayerLogout(PlayerEvent.PlayerLoggedOutEvent event) {
        if (event.getPlayer() instanceof ServerPlayerEntity) {
            ServerPlayerEntity player = (ServerPlayerEntity) event.getPlayer();
            BladeBallGameManager gameManager = BladeBallMod.getGameManager();
            if (gameManager != null) {
                gameManager.onPlayerDisconnect(player);
            }
        }
    }
    
    @SubscribeEvent
    public static void onPlayerRightClick(PlayerInteractEvent.RightClickEmpty event) {
        if (event.getPlayer() instanceof ServerPlayerEntity) {
            ServerPlayerEntity player = (ServerPlayerEntity) event.getPlayer();
            handlePlayerInteraction(player, event.getHand());
        }
    }
    
    @SubscribeEvent
    public static void onPlayerRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        if (event.getPlayer() instanceof ServerPlayerEntity) {
            ServerPlayerEntity player = (ServerPlayerEntity) event.getPlayer();
            BladeBallGameManager gameManager = BladeBallMod.getGameManager();
            if (gameManager != null && gameManager.getPlayerArena(player) != null) {
                // If player is in an arena, prioritize ball deflection over block interaction
                handlePlayerInteraction(player, event.getHand());
                event.setCanceled(true); // Prevent block interaction
            }
        }
    }
    
    @SubscribeEvent
    public static void onPlayerRightClickItem(PlayerInteractEvent.RightClickItem event) {
        if (event.getPlayer() instanceof ServerPlayerEntity) {
            ServerPlayerEntity player = (ServerPlayerEntity) event.getPlayer();
            BladeBallGameManager gameManager = BladeBallMod.getGameManager();
            if (gameManager != null && gameManager.getPlayerArena(player) != null) {
                // If player is in an arena, handle as ability use or ball deflection
                handlePlayerInteraction(player, event.getHand());
                event.setCanceled(true); // Prevent item use
            }
        }
    }
    
    /**
     * Handles player interactions (right-click) in the arena
     */
    private static void handlePlayerInteraction(ServerPlayerEntity player, Hand hand) {
        if (hand != Hand.MAIN_HAND) return; // Only handle main hand interactions
        
        BladeBallGameManager gameManager = BladeBallMod.getGameManager();
        if (gameManager == null) return;
        
        BladeBallArena arena = gameManager.getPlayerArena(player);
        if (arena == null) return; // Player not in arena
        
        BladeBallPlayerData playerData = gameManager.getPlayerData(player.getUniqueID());
        if (playerData == null) return;
        
        // Check if player is sneaking (shift) to use ability
        if (player.isSneaking()) {
            usePlayerAbility(player, playerData);
        } else {
            // Regular right-click is for ball deflection
            attemptBallDeflection(player, arena, playerData);
        }
    }
    
    /**
     * Attempts to use the player's selected ability
     */
    private static void usePlayerAbility(ServerPlayerEntity player, BladeBallPlayerData playerData) {
        if (!playerData.canUseAbility()) {
            long remainingMs = playerData.getRemainingCooldown();
            double remainingSeconds = remainingMs / 1000.0;
            player.sendMessage(new StringTextComponent(TextFormatting.RED + String.format("Ability on cooldown! %.1fs remaining", remainingSeconds)), player.getUniqueID());
            return;
        }
        
        if (playerData.getSelectedAbility() != null) {
            if (playerData.getSelectedAbility().execute(player)) {
                playerData.useAbility();
            }
        } else {
            player.sendMessage(new StringTextComponent(TextFormatting.RED + "No ability selected"), player.getUniqueID());
        }
    }
    
    /**
     * Attempts to deflect the ball
     */
    private static void attemptBallDeflection(ServerPlayerEntity player, BladeBallArena arena, BladeBallPlayerData playerData) {
        // This would interact with the ball entity when it's near the player
        // For now, we'll just show a message
        player.sendMessage(new StringTextComponent(TextFormatting.GRAY + "Deflection attempt! (Ball entity interaction will be implemented)"), player.getUniqueID());
        
        // TODO: Implement actual ball deflection logic
        // 1. Check if ball is within deflection range
        // 2. Calculate deflection direction based on player look direction
        // 3. Apply deflection to ball entity
        // 4. Update player stats
        // 5. Play effects and sounds
    }
    
    @SubscribeEvent
    public static void onPlayerJoin(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getPlayer() instanceof ServerPlayerEntity) {
            ServerPlayerEntity player = (ServerPlayerEntity) event.getPlayer();
            
            // Send welcome message
            player.sendMessage(new StringTextComponent(
                    TextFormatting.GOLD + "Welcome to Blade Ball!\n" +
                    TextFormatting.YELLOW + "Use " + TextFormatting.WHITE + "/bladeball help" + TextFormatting.YELLOW + " for commands\n" +
                    TextFormatting.GRAY + "Right-click to deflect the ball, Shift+Right-click to use abilities"
            ), player.getUniqueID());
        }
    }
}
