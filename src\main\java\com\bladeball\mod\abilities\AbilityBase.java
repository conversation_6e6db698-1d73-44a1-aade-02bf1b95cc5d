package com.bladeball.mod.abilities;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.util.text.StringTextComponent;
import net.minecraft.util.text.TextFormatting;

/**
 * Base class for all Blade Ball abilities
 */
public abstract class AbilityBase {
    protected final String name;
    protected final String description;
    protected final long cooldownMs;
    protected final AbilityType type;
    
    public AbilityBase(String name, String description, long cooldownMs, AbilityType type) {
        this.name = name;
        this.description = description;
        this.cooldownMs = cooldownMs;
        this.type = type;
    }
    
    /**
     * Executes the ability for the given player
     * @param player The player using the ability
     * @return true if the ability was successfully used, false otherwise
     */
    public abstract boolean execute(ServerPlayerEntity player);
    
    /**
     * Called when the ability effect should end (for abilities with duration)
     * @param player The player whose ability effect is ending
     */
    public void onEffectEnd(ServerPlayerEntity player) {
        // Default implementation does nothing
    }
    
    /**
     * Checks if the ability can be used by the player in their current state
     * @param player The player trying to use the ability
     * @return true if the ability can be used, false otherwise
     */
    public boolean canUse(ServerPlayerEntity player) {
        // Default implementation - can always use if not on cooldown
        return true;
    }
    
    /**
     * Sends a message to the player about the ability usage
     * @param player The player to send the message to
     * @param message The message to send
     * @param color The color of the message
     */
    protected void sendMessage(ServerPlayerEntity player, String message, TextFormatting color) {
        player.sendMessage(new StringTextComponent(color + message), player.getUUID());
    }
    
    /**
     * Sends a success message to the player
     */
    protected void sendSuccessMessage(ServerPlayerEntity player, String message) {
        sendMessage(player, message, TextFormatting.GREEN);
    }
    
    /**
     * Sends an error message to the player
     */
    protected void sendErrorMessage(ServerPlayerEntity player, String message) {
        sendMessage(player, message, TextFormatting.RED);
    }
    
    /**
     * Sends an info message to the player
     */
    protected void sendInfoMessage(ServerPlayerEntity player, String message) {
        sendMessage(player, message, TextFormatting.YELLOW);
    }
    
    // Getters
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public long getCooldownMs() {
        return cooldownMs;
    }
    
    public AbilityType getType() {
        return type;
    }
    
    public String getDisplayName() {
        TextFormatting color;
        switch (type) {
            case OFFENSIVE:
                color = TextFormatting.RED;
                break;
            case DEFENSIVE:
                color = TextFormatting.GREEN;
                break;
            case UTILITY:
                color = TextFormatting.BLUE;
                break;
            case MOVEMENT:
                color = TextFormatting.YELLOW;
                break;
            default:
                color = TextFormatting.WHITE;
                break;
        }

        return color + name + TextFormatting.RESET;
    }
    
    public String getFullDescription() {
        return String.format("%s\n%s%s\n%sCooldown: %.1fs",
                getDisplayName(),
                TextFormatting.GRAY,
                description,
                TextFormatting.DARK_GRAY,
                cooldownMs / 1000.0);
    }
    
    @Override
    public String toString() {
        return name;
    }
}
