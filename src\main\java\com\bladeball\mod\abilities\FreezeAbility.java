package com.bladeball.mod.abilities;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.world.server.ServerWorld;

import java.util.List;

/**
 * Freeze ability - freezes nearby players temporarily
 */
public class FreezeAbility extends AbilityBase {
    private static final double FREEZE_RADIUS = 8.0;
    private static final int FREEZE_DURATION = 60; // 3 seconds
    
    public FreezeAbility() {
        super("Freeze", 
              "Freeze all nearby players for 3 seconds", 
              25000, // 25 second cooldown
              AbilityType.OFFENSIVE);
    }
    
    @Override
    public boolean execute(ServerPlayerEntity player) {
        if (!canUse(player)) {
            return false;
        }
        
        ServerWorld world = player.getLevel();

        // Find nearby players
        AxisAlignedBB searchArea = new AxisAlignedBB(
                player.getX() - FREEZE_RADIUS, player.getY() - FREEZE_RADIUS, player.getZ() - FREEZE_RADIUS,
                player.getX() + FREEZE_RADIUS, player.getY() + FREEZE_RADIUS, player.getZ() + FREEZE_RADIUS
        );

        List<ServerPlayerEntity> nearbyPlayers = world.getEntitiesOfClass(ServerPlayerEntity.class, searchArea);
        int frozenCount = 0;
        
        for (ServerPlayerEntity target : nearbyPlayers) {
            if (target != player && target.distanceToSqr(player) <= FREEZE_RADIUS * FREEZE_RADIUS) {
                // Apply slowness effect to simulate freezing
                EffectInstance slowness = new EffectInstance(Effects.MOVEMENT_SLOWDOWN, FREEZE_DURATION, 4, false, false, true);
                target.addEffect(slowness);

                // Apply mining fatigue to prevent actions
                EffectInstance miningFatigue = new EffectInstance(Effects.DIG_SLOWDOWN, FREEZE_DURATION, 2, false, false, true);
                target.addEffect(miningFatigue);
                
                // Spawn freeze particles around target
                spawnFreezeParticles(target);
                
                sendInfoMessage(target, "You have been frozen!");
                frozenCount++;
            }
        }
        
        // Spawn area effect particles
        spawnAreaFreezeParticles(player);
        
        if (frozenCount > 0) {
            sendSuccessMessage(player, "Froze " + frozenCount + " player(s)!");
        } else {
            sendInfoMessage(player, "No players in range to freeze.");
        }
        
        return true;
    }
    
    private void spawnFreezeParticles(ServerPlayerEntity target) {
        ServerWorld world = target.getLevel();

        // Spawn ice particles around the frozen player
        for (int i = 0; i < 15; i++) {
            double offsetX = (Math.random() - 0.5) * 2;
            double offsetY = Math.random() * 2;
            double offsetZ = (Math.random() - 0.5) * 2;

            world.sendParticles(ParticleTypes.ITEM_SNOWBALL,
                    target.getX() + offsetX,
                    target.getY() + offsetY,
                    target.getZ() + offsetZ,
                    1, 0, 0, 0, 0);
        }
    }
    
    private void spawnAreaFreezeParticles(ServerPlayerEntity player) {
        ServerWorld world = player.getLevel();

        // Create a circle of particles to show the freeze area
        int particleCount = 40;
        for (int i = 0; i < particleCount; i++) {
            double angle = (2 * Math.PI * i) / particleCount;
            double x = player.getX() + FREEZE_RADIUS * Math.cos(angle);
            double z = player.getZ() + FREEZE_RADIUS * Math.sin(angle);

            world.sendParticles(ParticleTypes.CLOUD,
                    x, player.getY() + 0.1, z,
                    1, 0, 0, 0, 0);
        }

        // Central explosion effect
        world.sendParticles(ParticleTypes.EXPLOSION,
                player.getX(), player.getY() + 1, player.getZ(),
                5, 1, 1, 1, 0);
    }
    
    @Override
    public boolean canUse(ServerPlayerEntity player) {
        return super.canUse(player);
    }
}
