package com.bladeball.mod.abilities;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.util.math.vector.Vector3d;
import net.minecraft.world.server.ServerWorld;

/**
 * Dash ability - quickly moves the player forward
 */
public class DashAbility extends AbilityBase {
    private static final double DASH_DISTANCE = 5.0;
    private static final double DASH_HEIGHT = 0.5;
    
    public DashAbility() {
        super("Dash", 
              "Quickly dash forward in the direction you're looking", 
              3000, // 3 second cooldown
              AbilityType.MOVEMENT);
    }
    
    @Override
    public boolean execute(ServerPlayerEntity player) {
        if (!canUse(player)) {
            return false;
        }
        
        // Get player's look direction
        Vector3d lookDirection = player.getLookVec();
        
        // Calculate dash destination
        Vector3d currentPos = player.getPositionVec();
        Vector3d dashVector = lookDirection.scale(DASH_DISTANCE);
        Vector3d targetPos = currentPos.add(dashVector.x, DASH_HEIGHT, dashVector.z);
        
        // Check if target position is safe (basic check)
        if (!isSafePosition(player, targetPos)) {
            sendErrorMessage(player, "Cannot dash there!");
            return false;
        }
        
        // Perform the dash
        player.setPositionAndUpdate(targetPos.x, targetPos.y, targetPos.z);
        
        // Add particle effects
        spawnDashParticles(player, currentPos, targetPos);
        
        // Play sound effect (if available)
        // player.world.playSound(null, player.getPosition(), SoundEvents.ENTITY_ENDERMAN_TELEPORT, SoundCategory.PLAYERS, 0.5F, 1.0F);
        
        sendSuccessMessage(player, "Dash!");
        return true;
    }
    
    /**
     * Checks if the target position is safe to dash to
     */
    private boolean isSafePosition(ServerPlayerEntity player, Vector3d targetPos) {
        ServerWorld world = player.getServerWorld();
        
        // Check if the position is within world bounds
        if (targetPos.y < 0 || targetPos.y > world.getHeight()) {
            return false;
        }
        
        // Basic check for solid blocks (more sophisticated collision detection could be added)
        int x = (int) Math.floor(targetPos.x);
        int y = (int) Math.floor(targetPos.y);
        int z = (int) Math.floor(targetPos.z);
        
        // Check if there's space for the player (2 blocks high)
        for (int dy = 0; dy < 2; dy++) {
            if (world.getBlockState(new net.minecraft.util.math.BlockPos(x, y + dy, z)).isSolid()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Spawns particle effects for the dash
     */
    private void spawnDashParticles(ServerPlayerEntity player, Vector3d startPos, Vector3d endPos) {
        ServerWorld world = player.getServerWorld();
        
        // Create a trail of particles from start to end position
        Vector3d direction = endPos.subtract(startPos).normalize();
        double distance = startPos.distanceTo(endPos);
        int particleCount = (int) (distance * 3); // 3 particles per block
        
        for (int i = 0; i < particleCount; i++) {
            double progress = (double) i / particleCount;
            Vector3d particlePos = startPos.add(direction.scale(distance * progress));
            
            // Spawn cloud particles
            world.spawnParticle(ParticleTypes.CLOUD,
                    particlePos.x, particlePos.y + 1, particlePos.z,
                    5, // count
                    0.2, 0.2, 0.2, // spread
                    0.1); // speed
        }
        
        // Spawn impact particles at destination
        world.spawnParticle(ParticleTypes.POOF,
                endPos.x, endPos.y + 1, endPos.z,
                10, // count
                0.5, 0.5, 0.5, // spread
                0.2); // speed
    }
    
    @Override
    public boolean canUse(ServerPlayerEntity player) {
        // Can't dash if player is not on ground (prevents air dashing)
        if (!player.isOnGround()) {
            sendErrorMessage(player, "You must be on the ground to dash!");
            return false;
        }
        
        return super.canUse(player);
    }
}
