package com.bladeball.mod.abilities;

import net.minecraft.entity.player.ServerPlayerEntity;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.potion.EffectInstance;
import net.minecraft.potion.Effects;
import net.minecraft.world.server.ServerWorld;

/**
 * Invisibility ability - makes the player invisible for a short duration
 */
public class InvisibilityAbility extends AbilityBase {
    private static final int DURATION_TICKS = 100; // 5 seconds
    
    public InvisibilityAbility() {
        super("Invisibility", 
              "Become invisible for 5 seconds to avoid the ball", 
              15000, // 15 second cooldown
              AbilityType.DEFENSIVE);
    }
    
    @Override
    public boolean execute(ServerPlayerEntity player) {
        if (!canUse(player)) {
            return false;
        }
        
        // Apply invisibility effect
        EffectInstance invisibility = new EffectInstance(Effects.INVISIBILITY, DURATION_TICKS, 0, false, false, true);
        player.addPotionEffect(invisibility);
        
        // Spawn particles
        spawnInvisibilityParticles(player);
        
        sendSuccessMessage(player, "You are now invisible!");
        return true;
    }
    
    private void spawnInvisibilityParticles(ServerPlayerEntity player) {
        ServerWorld world = player.getServerWorld();
        
        // Spawn smoke particles around the player
        for (int i = 0; i < 20; i++) {
            double offsetX = (Math.random() - 0.5) * 2;
            double offsetY = Math.random() * 2;
            double offsetZ = (Math.random() - 0.5) * 2;
            
            world.spawnParticle(ParticleTypes.SMOKE,
                    player.getPosX() + offsetX, 
                    player.getPosY() + offsetY, 
                    player.getPosZ() + offsetZ,
                    1, 0, 0, 0, 0);
        }
    }
    
    @Override
    public boolean canUse(ServerPlayerEntity player) {
        // Can't use if already invisible
        if (player.isPotionActive(Effects.INVISIBILITY)) {
            sendErrorMessage(player, "You are already invisible!");
            return false;
        }
        
        return super.canUse(player);
    }
}
